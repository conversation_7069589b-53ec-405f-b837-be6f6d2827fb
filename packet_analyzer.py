#!/usr/bin/env python3
"""
Advanced Xenimus Packet Analyzer
Analyzes captured packets for potential vulnerabilities and patterns
"""

import json
import re
import struct
from collections import defaultdict, Counter
import hashlib

class XenimusPacketAnalyzer:
    def __init__(self, packet_file=None):
        self.packets = []
        self.patterns = defaultdict(list)
        self.vulnerabilities = []
        
        if packet_file:
            self.load_packets(packet_file)
    
    def load_packets(self, filename):
        """Load packets from JSON file"""
        try:
            with open(filename, 'r') as f:
                self.packets = json.load(f)
            print(f"Loaded {len(self.packets)} packets from {filename}")
        except Exception as e:
            print(f"Error loading packets: {e}")
    
    def find_authentication_patterns(self):
        """Look for authentication-related packets"""
        auth_keywords = [
            b'login', b'password', b'auth', b'token', b'session',
            b'user', b'pass', b'credential', b'key'
        ]
        
        auth_packets = []
        for packet in self.packets:
            payload = packet.get('payload', '')
            if payload:
                try:
                    payload_bytes = bytes.fromhex(payload)
                    payload_ascii = packet.get('payload_ascii', '')
                    
                    for keyword in auth_keywords:
                        if keyword in payload_bytes or keyword.decode() in payload_ascii.lower():
                            auth_packets.append({
                                'packet': packet,
                                'keyword': keyword.decode(),
                                'potential_vulnerability': 'Unencrypted authentication data'
                            })
                            break
                except:
                    continue
        
        return auth_packets
    
    def detect_buffer_overflow_attempts(self):
        """Detect potential buffer overflow patterns"""
        overflow_patterns = [
            b'A' * 100,  # Long sequences of same character
            b'\x90' * 50,  # NOP sleds
            b'\x41\x41\x41\x41',  # AAAA pattern
        ]
        
        suspicious_packets = []
        for packet in self.packets:
            payload = packet.get('payload', '')
            if payload and len(payload) > 200:  # Large packets
                try:
                    payload_bytes = bytes.fromhex(payload)
                    
                    # Check for repetitive patterns
                    for pattern in overflow_patterns:
                        if pattern in payload_bytes:
                            suspicious_packets.append({
                                'packet': packet,
                                'pattern': pattern.hex(),
                                'potential_vulnerability': 'Buffer overflow attempt'
                            })
                    
                    # Check for excessive length
                    if len(payload_bytes) > 1000:
                        suspicious_packets.append({
                            'packet': packet,
                            'pattern': 'excessive_length',
                            'potential_vulnerability': 'Potential buffer overflow (large payload)'
                        })
                        
                except:
                    continue
        
        return suspicious_packets
    
    def analyze_protocol_structure(self):
        """Analyze the game's protocol structure"""
        protocol_analysis = {
            'packet_sizes': Counter(),
            'common_headers': Counter(),
            'port_usage': Counter(),
            'timing_patterns': []
        }
        
        prev_timestamp = None
        for packet in self.packets:
            # Packet size distribution
            size = packet.get('length', 0)
            protocol_analysis['packet_sizes'][size] += 1
            
            # Port usage
            src_port = packet.get('src_port')
            dst_port = packet.get('dst_port')
            if src_port:
                protocol_analysis['port_usage'][src_port] += 1
            if dst_port:
                protocol_analysis['port_usage'][dst_port] += 1
            
            # Look for common headers (first few bytes)
            payload = packet.get('payload', '')
            if payload and len(payload) >= 8:
                header = payload[:8]  # First 4 bytes as hex
                protocol_analysis['common_headers'][header] += 1
            
            # Timing analysis
            if prev_timestamp:
                try:
                    curr_time = packet['timestamp']
                    # Simple timing difference (would need proper datetime parsing for accuracy)
                    protocol_analysis['timing_patterns'].append(curr_time)
                except:
                    pass
            prev_timestamp = packet.get('timestamp')
        
        return protocol_analysis
    
    def find_sql_injection_patterns(self):
        """Look for potential SQL injection attempts"""
        sql_patterns = [
            b"'", b'"', b'--', b'/*', b'*/', b'union', b'select',
            b'insert', b'update', b'delete', b'drop', b'exec',
            b'xp_', b'sp_', b'1=1', b'or 1=1', b'and 1=1'
        ]
        
        sql_packets = []
        for packet in self.packets:
            payload_ascii = packet.get('payload_ascii', '').lower()
            payload = packet.get('payload', '')
            
            if payload_ascii or payload:
                for pattern in sql_patterns:
                    pattern_str = pattern.decode()
                    if pattern_str in payload_ascii:
                        sql_packets.append({
                            'packet': packet,
                            'pattern': pattern_str,
                            'potential_vulnerability': 'SQL injection attempt'
                        })
                        break
        
        return sql_packets
    
    def detect_command_injection(self):
        """Look for command injection patterns"""
        cmd_patterns = [
            b';', b'|', b'&', b'`', b'$(', b'${',
            b'cmd', b'exec', b'system', b'shell',
            b'/bin/', b'powershell', b'cmd.exe'
        ]
        
        cmd_packets = []
        for packet in self.packets:
            payload_ascii = packet.get('payload_ascii', '').lower()
            
            if payload_ascii:
                for pattern in cmd_patterns:
                    pattern_str = pattern.decode()
                    if pattern_str in payload_ascii:
                        cmd_packets.append({
                            'packet': packet,
                            'pattern': pattern_str,
                            'potential_vulnerability': 'Command injection attempt'
                        })
                        break
        
        return cmd_packets
    
    def analyze_encryption(self):
        """Analyze if traffic appears encrypted"""
        encryption_analysis = {
            'likely_encrypted': 0,
            'likely_plaintext': 0,
            'entropy_scores': []
        }
        
        for packet in self.packets:
            payload = packet.get('payload', '')
            if payload:
                try:
                    payload_bytes = bytes.fromhex(payload)
                    
                    # Calculate entropy (simple measure)
                    if len(payload_bytes) > 10:
                        entropy = self.calculate_entropy(payload_bytes)
                        encryption_analysis['entropy_scores'].append(entropy)
                        
                        # High entropy might indicate encryption
                        if entropy > 7.5:
                            encryption_analysis['likely_encrypted'] += 1
                        else:
                            encryption_analysis['likely_plaintext'] += 1
                            
                except:
                    continue
        
        return encryption_analysis
    
    def calculate_entropy(self, data):
        """Calculate Shannon entropy of data"""
        if not data:
            return 0
        
        entropy = 0
        for x in range(256):
            p_x = float(data.count(x)) / len(data)
            if p_x > 0:
                entropy += - p_x * (p_x.bit_length() - 1)
        
        return entropy
    
    def generate_report(self):
        """Generate comprehensive analysis report"""
        print("=" * 60)
        print("XENIMUS PACKET ANALYSIS REPORT")
        print("=" * 60)
        
        # Authentication analysis
        auth_packets = self.find_authentication_patterns()
        print(f"\n🔐 AUTHENTICATION ANALYSIS:")
        print(f"Potential auth-related packets: {len(auth_packets)}")
        for auth in auth_packets[:5]:  # Show first 5
            print(f"  - Keyword: {auth['keyword']}, Vulnerability: {auth['potential_vulnerability']}")
        
        # Buffer overflow detection
        overflow_packets = self.detect_buffer_overflow_attempts()
        print(f"\n💥 BUFFER OVERFLOW ANALYSIS:")
        print(f"Suspicious packets: {len(overflow_packets)}")
        for overflow in overflow_packets[:3]:
            print(f"  - Pattern: {overflow['pattern'][:20]}..., Vulnerability: {overflow['potential_vulnerability']}")
        
        # SQL injection
        sql_packets = self.find_sql_injection_patterns()
        print(f"\n💉 SQL INJECTION ANALYSIS:")
        print(f"Potential SQL injection packets: {len(sql_packets)}")
        for sql in sql_packets[:3]:
            print(f"  - Pattern: {sql['pattern']}, Vulnerability: {sql['potential_vulnerability']}")
        
        # Command injection
        cmd_packets = self.detect_command_injection()
        print(f"\n⚡ COMMAND INJECTION ANALYSIS:")
        print(f"Potential command injection packets: {len(cmd_packets)}")
        for cmd in cmd_packets[:3]:
            print(f"  - Pattern: {cmd['pattern']}, Vulnerability: {cmd['potential_vulnerability']}")
        
        # Protocol analysis
        protocol = self.analyze_protocol_structure()
        print(f"\n📊 PROTOCOL ANALYSIS:")
        print(f"Most common packet sizes: {protocol['packet_sizes'].most_common(5)}")
        print(f"Most common headers: {protocol['common_headers'].most_common(3)}")
        print(f"Port usage: {protocol['port_usage'].most_common(5)}")
        
        # Encryption analysis
        encryption = self.analyze_encryption()
        print(f"\n🔒 ENCRYPTION ANALYSIS:")
        print(f"Likely encrypted packets: {encryption['likely_encrypted']}")
        print(f"Likely plaintext packets: {encryption['likely_plaintext']}")
        if encryption['entropy_scores']:
            avg_entropy = sum(encryption['entropy_scores']) / len(encryption['entropy_scores'])
            print(f"Average entropy: {avg_entropy:.2f}")

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description="Xenimus Packet Analyzer")
    parser.add_argument("packet_file", help="JSON file containing captured packets")
    
    args = parser.parse_args()
    
    analyzer = XenimusPacketAnalyzer(args.packet_file)
    analyzer.generate_report()

if __name__ == "__main__":
    main()
