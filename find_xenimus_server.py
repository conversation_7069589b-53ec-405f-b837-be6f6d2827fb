#!/usr/bin/env python3
"""
Xenimus Server Discovery Tool
Helps identify the game server IP and ports for targeted packet capture
"""

import subprocess
import re
import socket
import threading
import time
from scapy.all import *

class XenimusServerFinder:
    def __init__(self):
        self.potential_servers = []
        self.active_connections = []
        
    def get_active_connections(self):
        """Get current network connections"""
        try:
            # Windows netstat command
            result = subprocess.run(['netstat', '-an'], 
                                  capture_output=True, text=True)
            
            connections = []
            for line in result.stdout.split('\n'):
                if 'ESTABLISHED' in line:
                    parts = line.split()
                    if len(parts) >= 4:
                        local_addr = parts[1]
                        remote_addr = parts[2]
                        connections.append({
                            'local': local_addr,
                            'remote': remote_addr,
                            'protocol': parts[0] if parts[0] in ['TCP', 'UDP'] else 'TCP'
                        })
            
            return connections
        except Exception as e:
            print(f"Error getting connections: {e}")
            return []
    
    def monitor_connections(self, duration=30):
        """Monitor connections while game is running"""
        print(f"Monitoring connections for {duration} seconds...")
        print("Please start Xenimus now and perform some actions in-game")
        
        baseline_connections = set()
        game_connections = set()
        
        # Get baseline connections
        initial = self.get_active_connections()
        for conn in initial:
            baseline_connections.add(conn['remote'])
        
        print(f"Baseline connections: {len(baseline_connections)}")
        
        # Monitor for new connections
        start_time = time.time()
        while time.time() - start_time < duration:
            current = self.get_active_connections()
            for conn in current:
                remote = conn['remote']
                if remote not in baseline_connections:
                    game_connections.add(remote)
                    print(f"New connection detected: {remote}")
            
            time.sleep(2)
        
        return list(game_connections)
    
    def analyze_connection(self, address):
        """Analyze a specific connection"""
        try:
            ip, port = address.split(':')
            port = int(port)
            
            # Try to get hostname
            try:
                hostname = socket.gethostbyaddr(ip)[0]
            except:
                hostname = "Unknown"
            
            # Check if it's a common game port range
            is_game_port = 1000 <= port <= 65535
            
            return {
                'ip': ip,
                'port': port,
                'hostname': hostname,
                'likely_game_server': is_game_port,
                'address': address
            }
        except:
            return None
    
    def scan_common_game_ports(self, target_ip, port_range=(7000, 8000)):
        """Scan common game server ports"""
        print(f"Scanning {target_ip} for game servers on ports {port_range[0]}-{port_range[1]}...")
        
        open_ports = []
        for port in range(port_range[0], port_range[1] + 1):
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            
            try:
                result = sock.connect_ex((target_ip, port))
                if result == 0:
                    open_ports.append(port)
                    print(f"Found open port: {port}")
            except:
                pass
            finally:
                sock.close()
        
        return open_ports
    
    def create_wireshark_filter(self, servers):
        """Create Wireshark display filter for identified servers"""
        if not servers:
            return "tcp or udp"
        
        ip_filters = []
        for server in servers:
            analysis = self.analyze_connection(server)
            if analysis:
                ip_filters.append(f"ip.addr == {analysis['ip']}")
        
        if ip_filters:
            return " or ".join(ip_filters)
        else:
            return "tcp or udp"
    
    def create_scapy_filter(self, servers):
        """Create BPF filter for Scapy capture"""
        if not servers:
            return "tcp or udp"
        
        host_filters = []
        for server in servers:
            analysis = self.analyze_connection(server)
            if analysis:
                host_filters.append(f"host {analysis['ip']}")
        
        if host_filters:
            return " or ".join(host_filters)
        else:
            return "tcp or udp"
    
    def generate_capture_commands(self, servers):
        """Generate ready-to-use capture commands"""
        print("\n" + "="*60)
        print("CAPTURE COMMANDS")
        print("="*60)
        
        if not servers:
            print("No specific servers identified. Use general capture:")
            print("python packet_capture.py")
            return
        
        for server in servers:
            analysis = self.analyze_connection(server)
            if analysis:
                print(f"\nFor server {analysis['hostname']} ({analysis['address']}):")
                print(f"  Scapy capture:")
                print(f"    python packet_capture.py -t {analysis['ip']} -p {analysis['port']}")
                print(f"  Wireshark filter:")
                print(f"    ip.addr == {analysis['ip']} and tcp.port == {analysis['port']}")
    
    def run_discovery(self):
        """Run complete server discovery process"""
        print("🔍 Xenimus Server Discovery Tool")
        print("="*40)
        
        print("\n1. Getting baseline connections...")
        baseline = self.get_active_connections()
        print(f"   Found {len(baseline)} existing connections")
        
        print("\n2. Please start Xenimus and perform some actions...")
        input("   Press Enter when ready to start monitoring...")
        
        print("\n3. Monitoring for new connections...")
        game_servers = self.monitor_connections(30)
        
        if not game_servers:
            print("\n❌ No new connections detected!")
            print("   Try the following:")
            print("   - Make sure Xenimus is running")
            print("   - Perform actions in-game (move, chat, etc.)")
            print("   - Check if game uses existing connections")
            return
        
        print(f"\n✅ Found {len(game_servers)} potential game servers:")
        
        analyzed_servers = []
        for server in game_servers:
            analysis = self.analyze_connection(server)
            if analysis:
                analyzed_servers.append(analysis)
                print(f"   {analysis['address']} ({analysis['hostname']})")
        
        # Generate filters and commands
        wireshark_filter = self.create_wireshark_filter(game_servers)
        scapy_filter = self.create_scapy_filter(game_servers)
        
        print(f"\n📋 FILTERS:")
        print(f"Wireshark: {wireshark_filter}")
        print(f"Scapy BPF: {scapy_filter}")
        
        self.generate_capture_commands(game_servers)
        
        # Save results
        with open("xenimus_servers.txt", "w") as f:
            f.write("Xenimus Server Discovery Results\n")
            f.write("="*40 + "\n\n")
            for analysis in analyzed_servers:
                f.write(f"Server: {analysis['address']}\n")
                f.write(f"IP: {analysis['ip']}\n")
                f.write(f"Port: {analysis['port']}\n")
                f.write(f"Hostname: {analysis['hostname']}\n")
                f.write(f"Likely Game Server: {analysis['likely_game_server']}\n")
                f.write("-" * 30 + "\n")
            
            f.write(f"\nWireshark Filter: {wireshark_filter}\n")
            f.write(f"Scapy Filter: {scapy_filter}\n")
        
        print(f"\n💾 Results saved to xenimus_servers.txt")

def main():
    finder = XenimusServerFinder()
    finder.run_discovery()

if __name__ == "__main__":
    main()
