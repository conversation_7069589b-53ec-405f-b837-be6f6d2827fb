@echo off
echo Starting Xenimus packet capture for server ************
echo Make sure to run this as Administrator for packet capture to work
echo.
echo Starting capture... Press Ctrl+C to stop
REM Try different Python commands
echo Checking for Python installation...

where python >nul 2>&1
if %errorlevel% == 0 (
    echo Found python command
    python packet_capture.py -t ************ -o captures/xenimus_session_%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%.json
    goto :end
)

where python3 >nul 2>&1
if %errorlevel% == 0 (
    echo Found python3 command
    python3 packet_capture.py -t ************ -o captures/xenimus_session_%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%.json
    goto :end
)

where py >nul 2>&1
if %errorlevel% == 0 (
    echo Found py launcher
    py packet_capture.py -t ************ -o captures/xenimus_session_%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%.json
    goto :end
)

echo ERROR: Python not found in PATH!
echo.
echo Please try one of these solutions:
echo 1. Install Python from https://python.org (make sure to check "Add to PATH")
echo 2. Use the Python Launcher: py packet_capture.py -t ************ -o captures/xenimus_session.json
echo 3. Use Wireshark with filter: ip.addr == ************
echo.

:end
pause
