
#!/usr/bin/env python3
"""
Xenimus Packet Capture and Analysis Tool
Captures and analyzes network packets for penetration testing
"""

from scapy.all import *
import json
import time
from datetime import datetime

class XenimusPacketCapture:
    def __init__(self, interface=None, target_ip=None, target_port=None):
        self.interface = interface
        self.target_ip = target_ip
        self.target_port = target_port
        self.captured_packets = []
        
    def packet_handler(self, packet):
        """Process each captured packet"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")
        
        # Extract basic packet info
        packet_info = {
            'timestamp': timestamp,
            'src': packet[IP].src if IP in packet else None,
            'dst': packet[IP].dst if IP in packet else None,
            'protocol': packet[IP].proto if IP in packet else None,
            'length': len(packet)
        }
        
        # TCP specific info
        if TCP in packet:
            packet_info.update({
                'src_port': packet[TCP].sport,
                'dst_port': packet[TCP].dport,
                'tcp_flags': packet[TCP].flags,
                'seq': packet[TCP].seq,
                'ack': packet[TCP].ack
            })
            
        # UDP specific info
        elif UDP in packet:
            packet_info.update({
                'src_port': packet[UDP].sport,
                'dst_port': packet[UDP].dport
            })
        
        # Raw payload
        if Raw in packet:
            packet_info['payload'] = packet[Raw].load.hex()
            packet_info['payload_ascii'] = self.safe_ascii(packet[Raw].load)
        
        self.captured_packets.append(packet_info)
        
        # Real-time display
        print(f"[{timestamp}] {packet_info['src']}:{packet_info.get('src_port', 'N/A')} -> "
              f"{packet_info['dst']}:{packet_info.get('dst_port', 'N/A')} "
              f"({packet_info['length']} bytes)")
        
        if packet_info.get('payload_ascii'):
            print(f"  ASCII: {packet_info['payload_ascii'][:100]}...")
    
    def safe_ascii(self, data):
        """Convert bytes to safe ASCII representation"""
        try:
            return ''.join(chr(b) if 32 <= b <= 126 else '.' for b in data)
        except:
            return str(data)
    
    def create_filter(self):
        """Create BPF filter for packet capture"""
        filters = []
        
        if self.target_ip:
            filters.append(f"host {self.target_ip}")
        
        if self.target_port:
            filters.append(f"port {self.target_port}")
        
        # Default to TCP/UDP if no specific filter
        if not filters:
            filters.append("tcp or udp")
        
        return " and ".join(filters)
    
    def start_capture(self, count=0):
        """Start packet capture"""
        filter_str = self.create_filter()
        print(f"Starting capture with filter: {filter_str}")
        print(f"Interface: {self.interface or 'default'}")
        print("Press Ctrl+C to stop...\n")
        
        try:
            sniff(
                iface=self.interface,
                filter=filter_str,
                prn=self.packet_handler,
                count=count,
                store=0
            )
        except KeyboardInterrupt:
            print(f"\nCapture stopped. Captured {len(self.captured_packets)} packets.")
    
    def save_capture(self, filename):
        """Save captured packets to JSON file"""
        with open(filename, 'w') as f:
            json.dump(self.captured_packets, f, indent=2)
        print(f"Packets saved to {filename}")
    
    def analyze_patterns(self):
        """Basic pattern analysis"""
        if not self.captured_packets:
            print("No packets to analyze")
            return
        
        print("\n=== PACKET ANALYSIS ===")
        print(f"Total packets: {len(self.captured_packets)}")
        
        # Protocol distribution
        protocols = {}
        for packet in self.captured_packets:
            proto = packet.get('protocol', 'Unknown')
            protocols[proto] = protocols.get(proto, 0) + 1
        
        print("\nProtocol distribution:")
        for proto, count in protocols.items():
            print(f"  {proto}: {count}")
        
        # Port analysis
        ports = {}
        for packet in self.captured_packets:
            src_port = packet.get('src_port')
            dst_port = packet.get('dst_port')
            if src_port:
                ports[src_port] = ports.get(src_port, 0) + 1
            if dst_port:
                ports[dst_port] = ports.get(dst_port, 0) + 1
        
        print("\nTop ports:")
        sorted_ports = sorted(ports.items(), key=lambda x: x[1], reverse=True)[:10]
        for port, count in sorted_ports:
            print(f"  {port}: {count}")

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description="Xenimus Packet Capture Tool")
    parser.add_argument("-i", "--interface", help="Network interface to capture on")
    parser.add_argument("-t", "--target-ip", help="Target IP address to filter")
    parser.add_argument("-p", "--target-port", type=int, help="Target port to filter")
    parser.add_argument("-c", "--count", type=int, default=0, help="Number of packets to capture (0 = unlimited)")
    parser.add_argument("-o", "--output", help="Output file for captured packets")
    
    args = parser.parse_args()
    
    # Create capture instance
    capture = XenimusPacketCapture(
        interface=args.interface,
        target_ip=args.target_ip,
        target_port=args.target_port
    )
    
    # Start capture
    capture.start_capture(count=args.count)
    
    # Analyze and save
    capture.analyze_patterns()
    
    if args.output:
        capture.save_capture(args.output)

if __name__ == "__main__":
    main()

