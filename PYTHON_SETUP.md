# Python Setup for Xenimus Packet Capture

## Problem: "Python is not found by terminal"

### Quick Solutions

#### Option 1: Use Python Launcher (Recommended)
Try this command instead:
```cmd
py packet_capture.py -t ************ -o captures/xenimus_session.json
```

#### Option 2: Use PowerShell Script
```powershell
PowerShell -ExecutionPolicy Bypass -File capture_xenimus.ps1
```

#### Option 3: Find Your Python Installation
Check these common locations:
- `C:\Python39\python.exe`
- `C:\Python310\python.exe`
- `C:\Python311\python.exe`
- `C:\Python312\python.exe`
- `%LOCALAPPDATA%\Programs\Python\Python3X\python.exe`

Then run:
```cmd
C:\Python3X\python.exe packet_capture.py -t ************ -o captures/xenimus_session.json
```

### Install Python Properly

1. **Download Python**: https://python.org/downloads/
2. **IMPORTANT**: Check "Add Python to PATH" during installation
3. **Verify**: Open new command prompt and type `python --version`

### Install Required Packages

Once Python is working:
```cmd
pip install scapy requests colorama
```

### Alternative: Use Wireshark

If Python setup is too complex, use Wireshark:

1. **Download**: https://wireshark.org/
2. **Install** with Npcap
3. **Start capture** on your network interface
4. **Filter**: `ip.addr == ************`
5. **Start Xenimus** and perform actions
6. **Stop capture** and analyze packets

### Manual Commands for Xenimus Server ************

```cmd
# Basic capture
python packet_capture.py -t ************ -o captures/xenimus_session.json

# With specific port (if you know it)
python packet_capture.py -t ************ -p 7777 -o captures/xenimus_session.json

# Analyze captured packets
python packet_analyzer.py captures/xenimus_session.json

# Interactive packet manipulation
python packet_manipulator.py captures/xenimus_session.json --interactive
```

### Troubleshooting

**Error: "scapy not found"**
```cmd
pip install scapy
```

**Error: "Permission denied"**
- Run Command Prompt as Administrator
- Or use PowerShell as Administrator

**Error: "No module named 'scapy'"**
```cmd
python -m pip install scapy
```

### Quick Test
```cmd
python -c "import scapy; print('Scapy is working!')"
```

If this works, you're ready for packet capture!
