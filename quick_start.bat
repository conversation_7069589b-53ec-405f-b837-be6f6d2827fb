@echo off
echo Quick Start - Xenimus Packet Capture
echo ====================================

REM Show current directory
echo Current directory: %CD%

REM List Python files to verify we're in the right place
echo.
echo Python files in current directory:
dir *.py /b

REM Check if we have the required files
if not exist "packet_capture.py" (
    echo.
    echo ERROR: packet_capture.py not found!
    echo Make sure you're in the correct directory with the Xenimus tools
    pause
    exit /b 1
)

REM Create captures directory
if not exist "captures" mkdir captures

echo.
echo Installing Scapy (required for packet capture)...
python -m pip install scapy

echo.
echo Testing Scapy installation...
python -c "import scapy; print('Scapy installed successfully')"
if %errorlevel% neq 0 (
    echo Scapy installation failed!
    echo Try running as Administrator
    pause
    exit /b 1
)

echo.
echo ====================================
echo READY TO CAPTURE PACKETS!
echo ====================================
echo.
echo Target server: *************
echo Output file: captures\xenimus_session.json
echo.
echo IMPORTANT: 
echo 1. Make sure you're running as Administrator
echo 2. Start this capture BEFORE launching Xenimus
echo 3. Press Ctrl+C to stop capture
echo.
echo Starting packet capture in 5 seconds...
timeout /t 5 /nobreak

echo.
echo Starting packet capture...
python packet_capture.py -t ************* -o captures\xenimus_session.json

echo.
echo Packet capture finished!
echo Check captures\xenimus_session.json for results
echo.
echo To analyze the captured packets, run:
echo python packet_analyzer.py captures\xenimus_session.json
echo.
pause
