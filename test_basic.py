#!/usr/bin/env python3
"""
Basic test to see if Python is working and can create files
"""

import os
import sys
import json
from datetime import datetime

def main():
    print("🧪 Basic Python Test")
    print("=" * 30)
    
    print(f"Python version: {sys.version}")
    print(f"Current directory: {os.getcwd()}")
    
    # Create captures directory
    try:
        os.makedirs("captures", exist_ok=True)
        print("✅ Created captures directory")
    except Exception as e:
        print(f"❌ Failed to create captures directory: {e}")
    
    # Test JSON writing
    try:
        test_data = {
            "timestamp": datetime.now().isoformat(),
            "test": "Basic functionality working",
            "target_ip": "*************"
        }
        
        with open("captures/test_basic.json", "w") as f:
            json.dump(test_data, f, indent=2)
        
        print("✅ JSON file creation successful")
        print("✅ Basic Python functionality is working")
        
    except Exception as e:
        print(f"❌ JSON test failed: {e}")
    
    # Test scapy import
    print("\nTesting Scapy import...")
    try:
        import scapy
        print("✅ Scapy imported successfully")
        
        # Try importing specific scapy modules
        from scapy.all import IP, TCP, UDP
        print("✅ Scapy modules imported successfully")
        
        print("\n🚀 Ready to run packet capture!")
        print("Try: python packet_capture.py -t ************* -o captures/xenimus_test.json")
        
    except ImportError as e:
        print(f"❌ Scapy not installed: {e}")
        print("\nInstall Scapy with one of these commands:")
        print("  pip install scapy")
        print("  python -m pip install scapy")
        print("  py -m pip install scapy")
        
        print("\nOr try the simple version without Scapy:")
        print("  python simple_packet_capture.py")
    
    except Exception as e:
        print(f"❌ Scapy error: {e}")

if __name__ == "__main__":
    main()
