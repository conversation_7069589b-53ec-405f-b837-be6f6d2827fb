#!/usr/bin/env python3
"""
Setup script for Xenimus penetration testing tools
"""

import subprocess
import sys
import os

def install_package(package):
    """Install a Python package using pip"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ Successfully installed {package}")
        return True
    except subprocess.CalledProcessError:
        print(f"❌ Failed to install {package}")
        return False

def check_admin():
    """Check if running with admin privileges (needed for packet capture)"""
    try:
        return os.getuid() == 0
    except AttributeError:
        # Windows
        import ctypes
        return ctypes.windll.shell32.IsUserAnAdmin() != 0

def main():
    print("🔧 Setting up Xenimus Penetration Testing Tools")
    print("=" * 50)
    
    # Check admin privileges
    if not check_admin():
        print("⚠️  WARNING: You may need administrator privileges for packet capture")
        print("   Consider running as administrator/root for full functionality")
    
    # Required packages
    packages = [
        "scapy",
        "requests",
        "beautifulsoup4",
        "colorama",
        "python-nmap",
        "pycryptodome"
    ]
    
    print("\n📦 Installing required Python packages...")
    failed_packages = []
    
    for package in packages:
        if not install_package(package):
            failed_packages.append(package)
    
    if failed_packages:
        print(f"\n❌ Failed to install: {', '.join(failed_packages)}")
        print("Try installing manually with: pip install <package_name>")
    else:
        print("\n✅ All packages installed successfully!")
    
    # Create directories
    print("\n📁 Creating project directories...")
    directories = [
        "captures",
        "reports",
        "exploits",
        "tools"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"Created: {directory}/")
    
    # Create usage examples
    print("\n📝 Creating usage examples...")
    
    usage_example = """# Xenimus Penetration Testing Usage Examples

## 1. Basic Packet Capture
```bash
# Capture all traffic (run as admin)
python packet_capture.py

# Capture traffic to specific IP
python packet_capture.py -t *************

# Capture traffic on specific port
python packet_capture.py -p 7777

# Save capture to file
python packet_capture.py -o captures/xenimus_session1.json
```

## 2. Packet Analysis
```bash
# Analyze captured packets
python packet_analyzer.py captures/xenimus_session1.json
```

## 3. Finding Xenimus Server
```bash
# Use netstat to find active connections while game is running
netstat -an | findstr ESTABLISHED

# Or use nmap to scan for game servers
nmap -p 1000-9999 [target_ip_range]
```

## 4. Wireshark Filters for Xenimus
```
# Filter by game traffic (adjust IP as needed)
ip.addr == [xenimus_server_ip]

# Filter by common game ports
tcp.port >= 7000 and tcp.port <= 8000

# Look for authentication packets
tcp contains "login" or tcp contains "password"
```

## Tips for Effective Analysis
1. Start packet capture BEFORE launching Xenimus
2. Perform specific actions in-game while capturing
3. Look for patterns in login, character movement, chat, etc.
4. Check for unencrypted sensitive data
5. Test for packet replay attacks
6. Monitor for buffer overflow opportunities
"""
    
    with open("USAGE.md", "w") as f:
        f.write(usage_example)
    
    print("✅ Setup complete!")
    print("\n🚀 Next steps:")
    print("1. Read USAGE.md for examples")
    print("2. Start Xenimus game")
    print("3. Run: python packet_capture.py -o captures/session1.json")
    print("4. Perform actions in game")
    print("5. Stop capture (Ctrl+C)")
    print("6. Analyze: python packet_analyzer.py captures/session1.json")

if __name__ == "__main__":
    main()
