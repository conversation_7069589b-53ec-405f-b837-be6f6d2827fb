@echo off
echo Xenimus Packet Capture Setup and Run
echo ====================================

REM Create captures directory
if not exist "captures" (
    mkdir captures
    echo Created captures directory
)

echo.
echo Step 1: Testing basic Python functionality...
python test_basic.py
if %errorlevel% neq 0 (
    echo Python test failed!
    pause
    exit /b 1
)

echo.
echo Step 2: Installing Scapy (this may take a moment)...
python -m pip install scapy
if %errorlevel% neq 0 (
    echo Scapy installation failed, trying alternative...
    pip install scapy
)

echo.
echo Step 3: Testing packet capture...
echo This will capture packets from Xenimus server *************
echo Press Ctrl+C to stop the capture
echo.
echo Starting in 3 seconds...
timeout /t 3 /nobreak >nul

python packet_capture.py -t ************* -o captures/xenimus_session.json

echo.
echo Packet capture finished!
echo Check the captures/ directory for results
echo.
pause
