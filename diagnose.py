#!/usr/bin/env python3
"""
Quick diagnostic script for packet capture issues
"""

import sys
import os

def main():
    print("🔍 Xenimus Packet Capture Diagnostics")
    print("=" * 40)
    
    # Check Python version
    print(f"Python version: {sys.version}")
    print(f"Python executable: {sys.executable}")
    
    # Check if running as admin
    try:
        import ctypes
        is_admin = ctypes.windll.shell32.IsUserAnAdmin() != 0
        print(f"Running as admin: {is_admin}")
    except:
        print("Admin check failed (not Windows?)")
    
    # Check current directory
    print(f"Current directory: {os.getcwd()}")
    
    # Check if packet_capture.py exists
    if os.path.exists("packet_capture.py"):
        print("✅ packet_capture.py found")
    else:
        print("❌ packet_capture.py not found")
    
    # Try importing scapy
    print("\nTesting Scapy import...")
    try:
        import scapy
        print("✅ Scapy imported successfully")
        print(f"Scapy version: {scapy.__version__}")
    except ImportError as e:
        print(f"❌ Scapy import failed: {e}")
        print("Install with: pip install scapy")
    except Exception as e:
        print(f"❌ Scapy error: {e}")
    
    # Try importing scapy.all
    print("\nTesting Scapy.all import...")
    try:
        from scapy.all import IP, TCP, UDP, sniff
        print("✅ Scapy.all imported successfully")
    except ImportError as e:
        print(f"❌ Scapy.all import failed: {e}")
    except Exception as e:
        print(f"❌ Scapy.all error: {e}")
    
    # Check other imports
    modules = ['json', 'time', 'datetime', 'argparse']
    for module in modules:
        try:
            __import__(module)
            print(f"✅ {module} - OK")
        except ImportError:
            print(f"❌ {module} - Failed")
    
    print("\n" + "=" * 40)
    print("RECOMMENDATIONS:")
    
    # Check if scapy failed
    try:
        import scapy
    except:
        print("1. Install Scapy: pip install scapy")
        print("2. Or try: python -m pip install scapy")
    
    # Check admin
    try:
        import ctypes
        if not ctypes.windll.shell32.IsUserAnAdmin():
            print("3. Run as Administrator for packet capture")
    except:
        pass
    
    print("4. Try the simple version: python simple_packet_capture.py")

if __name__ == "__main__":
    main()
