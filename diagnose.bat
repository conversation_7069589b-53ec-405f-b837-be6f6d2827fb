@echo off
echo Xenimus Packet Capture Diagnostics
echo ===================================

echo.
echo Checking Python installation...
python --version
if %errorlevel% neq 0 (
    echo Python not found in PATH
    echo Trying python3...
    python3 --version
    if %errorlevel% neq 0 (
        echo Python3 not found either
        echo Trying py launcher...
        py --version
        if %errorlevel% neq 0 (
            echo No Python found! Please install Python from python.org
            goto :end
        ) else (
            echo Found Python via py launcher
            set PYTHON_CMD=py
        )
    ) else (
        echo Found python3
        set PYTHON_CMD=python3
    )
) else (
    echo Found python
    set PYTHON_CMD=python
)

echo.
echo Testing Python imports...
%PYTHON_CMD% -c "import sys; print('Python executable:', sys.executable)"
%PYTHON_CMD% -c "import json; print('JSON: OK')"
%PYTHON_CMD% -c "import time; print('Time: OK')"

echo.
echo Testing Scapy...
%PYTHON_CMD% -c "import scapy; print('Scapy: OK')"
if %errorlevel% neq 0 (
    echo Scapy not installed!
    echo Installing Scapy...
    %PYTHON_CMD% -m pip install scapy
)

echo.
echo Testing packet_capture.py syntax...
%PYTHON_CMD% -m py_compile packet_capture.py
if %errorlevel% neq 0 (
    echo Syntax error in packet_capture.py
) else (
    echo packet_capture.py syntax is OK
)

echo.
echo Checking admin privileges...
net session >nul 2>&1
if %errorlevel% == 0 (
    echo Running as Administrator: YES
) else (
    echo Running as Administrator: NO
    echo Packet capture requires admin privileges
)

echo.
echo Files in current directory:
dir *.py

echo.
echo ===================================
echo SUMMARY:
echo Python command to use: %PYTHON_CMD%
echo.
echo To run packet capture:
echo %PYTHON_CMD% packet_capture.py -t ************* -o captures/test.json
echo.
echo Or try the simple version:
echo %PYTHON_CMD% simple_packet_capture.py

:end
pause
