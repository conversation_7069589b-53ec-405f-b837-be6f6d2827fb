#!/usr/bin/env python3
"""
Install dependencies for Xenimus packet capture tools
"""

import subprocess
import sys
import os

def run_command(command):
    """Run a command and return success status"""
    try:
        print(f"Running: {command}")
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Success")
            if result.stdout:
                print(result.stdout)
            return True
        else:
            print("❌ Failed")
            if result.stderr:
                print(f"Error: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def check_python():
    """Check Python installation"""
    print("🐍 Checking Python installation...")
    
    try:
        version = sys.version
        print(f"✅ Python version: {version}")
        return True
    except:
        print("❌ Python not found")
        return False

def install_pip_packages():
    """Install required pip packages"""
    packages = [
        "scapy",
        "requests", 
        "colorama"
    ]
    
    print("\n📦 Installing Python packages...")
    
    failed_packages = []
    
    for package in packages:
        print(f"\nInstalling {package}...")
        
        # Try different pip commands
        commands = [
            f"pip install {package}",
            f"python -m pip install {package}",
            f"py -m pip install {package}"
        ]
        
        success = False
        for cmd in commands:
            if run_command(cmd):
                success = True
                break
        
        if not success:
            failed_packages.append(package)
    
    return failed_packages

def test_imports():
    """Test if packages can be imported"""
    print("\n🧪 Testing package imports...")
    
    test_packages = {
        "scapy": "from scapy.all import *",
        "requests": "import requests",
        "json": "import json",
        "socket": "import socket"
    }
    
    for package, import_cmd in test_packages.items():
        try:
            exec(import_cmd)
            print(f"✅ {package} - OK")
        except ImportError as e:
            print(f"❌ {package} - Failed: {e}")
        except Exception as e:
            print(f"⚠️  {package} - Warning: {e}")

def check_admin_rights():
    """Check if running with admin rights"""
    print("\n🔐 Checking administrator privileges...")
    
    try:
        # Windows
        import ctypes
        is_admin = ctypes.windll.shell32.IsUserAnAdmin() != 0
    except:
        # Unix-like
        is_admin = os.getuid() == 0
    
    if is_admin:
        print("✅ Running with administrator privileges")
    else:
        print("⚠️  Not running as administrator")
        print("   Packet capture may require admin rights")
    
    return is_admin

def create_directories():
    """Create necessary directories"""
    print("\n📁 Creating directories...")
    
    directories = ["captures", "reports", "logs"]
    
    for directory in directories:
        try:
            os.makedirs(directory, exist_ok=True)
            print(f"✅ Created/verified: {directory}/")
        except Exception as e:
            print(f"❌ Failed to create {directory}/: {e}")

def main():
    print("🔧 Xenimus Packet Capture - Dependency Installer")
    print("=" * 50)
    
    # Check Python
    if not check_python():
        print("\n❌ Python is required but not found!")
        print("Please install Python from https://python.org")
        return
    
    # Check admin rights
    is_admin = check_admin_rights()
    
    # Create directories
    create_directories()
    
    # Install packages
    failed_packages = install_pip_packages()
    
    # Test imports
    test_imports()
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 INSTALLATION SUMMARY")
    print("=" * 50)
    
    if failed_packages:
        print(f"❌ Failed to install: {', '.join(failed_packages)}")
        print("\nTry installing manually:")
        for package in failed_packages:
            print(f"  pip install {package}")
    else:
        print("✅ All packages installed successfully!")
    
    if not is_admin:
        print("\n⚠️  IMPORTANT: Run as Administrator for packet capture")
    
    print("\n🚀 Next steps:")
    print("1. Run as Administrator if not already")
    print("2. Test with: python simple_packet_capture.py")
    print("3. For full capture: python packet_capture.py -t *************")
    
    # Test simple capture
    print("\n🧪 Testing simple packet capture...")
    try:
        import simple_packet_capture
        print("✅ simple_packet_capture.py can be imported")
    except Exception as e:
        print(f"❌ Error importing simple_packet_capture.py: {e}")

if __name__ == "__main__":
    main()
