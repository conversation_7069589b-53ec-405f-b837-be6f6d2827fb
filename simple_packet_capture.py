#!/usr/bin/env python3
"""
Simple Xenimus Packet Capture Tool
Simplified version with better error handling and fewer dependencies
"""

import sys
import json
import time
import socket
from datetime import datetime

def check_dependencies():
    """Check if required dependencies are available"""
    missing = []
    
    try:
        import scapy
        print("✅ Scapy is available")
    except ImportError:
        missing.append("scapy")
        print("❌ Scapy not found")
    
    if missing:
        print(f"\nMissing dependencies: {', '.join(missing)}")
        print("Install with: pip install " + " ".join(missing))
        return False
    
    return True

def simple_network_monitor(target_ip, duration=60):
    """Simple network monitoring without scapy"""
    print(f"Monitoring network connections to {target_ip} for {duration} seconds...")
    print("This is a basic monitor - for full packet capture, install scapy")
    
    connections = []
    start_time = time.time()
    
    while time.time() - start_time < duration:
        try:
            # Try to connect to common game ports
            common_ports = [7777, 8080, 9999, 23456, 27015]
            
            for port in common_ports:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(1)
                
                try:
                    result = sock.connect_ex((target_ip, port))
                    if result == 0:
                        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                        connection_info = {
                            'timestamp': timestamp,
                            'target_ip': target_ip,
                            'port': port,
                            'status': 'open'
                        }
                        connections.append(connection_info)
                        print(f"[{timestamp}] Found open port: {target_ip}:{port}")
                except:
                    pass
                finally:
                    sock.close()
            
            time.sleep(5)  # Check every 5 seconds
            
        except KeyboardInterrupt:
            print("\nMonitoring stopped by user")
            break
        except Exception as e:
            print(f"Error during monitoring: {e}")
    
    return connections

def scapy_packet_capture(target_ip, output_file, count=100):
    """Full packet capture using scapy"""
    try:
        from scapy.all import sniff, IP, TCP, UDP, Raw
        
        captured_packets = []
        
        def packet_handler(packet):
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")
            
            # Extract basic packet info
            packet_info = {
                'timestamp': timestamp,
                'length': len(packet)
            }
            
            if IP in packet:
                packet_info.update({
                    'src': packet[IP].src,
                    'dst': packet[IP].dst,
                    'protocol': packet[IP].proto
                })
                
                # TCP specific info
                if TCP in packet:
                    packet_info.update({
                        'src_port': packet[TCP].sport,
                        'dst_port': packet[TCP].dport,
                        'tcp_flags': packet[TCP].flags
                    })
                    
                # UDP specific info
                elif UDP in packet:
                    packet_info.update({
                        'src_port': packet[UDP].sport,
                        'dst_port': packet[UDP].dport
                    })
                
                # Raw payload
                if Raw in packet:
                    payload_bytes = packet[Raw].load
                    packet_info['payload'] = payload_bytes.hex()
                    packet_info['payload_ascii'] = ''.join(
                        chr(b) if 32 <= b <= 126 else '.' for b in payload_bytes
                    )
            
            captured_packets.append(packet_info)
            
            # Real-time display
            src = packet_info.get('src', 'Unknown')
            dst = packet_info.get('dst', 'Unknown')
            src_port = packet_info.get('src_port', 'N/A')
            dst_port = packet_info.get('dst_port', 'N/A')
            
            print(f"[{timestamp}] {src}:{src_port} -> {dst}:{dst_port} ({packet_info['length']} bytes)")
        
        # Create filter for target IP
        filter_str = f"host {target_ip}"
        
        print(f"Starting packet capture for {target_ip}")
        print(f"Filter: {filter_str}")
        print(f"Capturing {count} packets (0 = unlimited)")
        print("Press Ctrl+C to stop...\n")
        
        # Start capture
        sniff(
            filter=filter_str,
            prn=packet_handler,
            count=count,
            store=0
        )
        
        # Save results
        if captured_packets and output_file:
            with open(output_file, 'w') as f:
                json.dump(captured_packets, f, indent=2)
            print(f"\nCaptured {len(captured_packets)} packets")
            print(f"Saved to: {output_file}")
        
        return captured_packets
        
    except ImportError:
        print("Scapy not available, falling back to simple monitoring")
        return simple_network_monitor(target_ip)
    except Exception as e:
        print(f"Error during packet capture: {e}")
        return []

def main():
    print("🔍 Simple Xenimus Packet Capture Tool")
    print("=" * 40)
    
    # Default values
    target_ip = "*************"  # Current Xenimus server
    output_file = f"captures/xenimus_simple_{int(time.time())}.json"
    
    # Check if captures directory exists
    import os
    os.makedirs("captures", exist_ok=True)
    
    # Check dependencies
    has_scapy = check_dependencies()
    
    print(f"\nTarget IP: {target_ip}")
    print(f"Output file: {output_file}")
    
    if len(sys.argv) > 1:
        target_ip = sys.argv[1]
        print(f"Using custom target IP: {target_ip}")
    
    try:
        if has_scapy:
            print("\n🚀 Starting full packet capture with Scapy...")
            packets = scapy_packet_capture(target_ip, output_file, count=0)
        else:
            print("\n🔍 Starting simple network monitoring...")
            connections = simple_network_monitor(target_ip, duration=120)
            
            # Save simple monitoring results
            if connections:
                with open(output_file, 'w') as f:
                    json.dump(connections, f, indent=2)
                print(f"Saved {len(connections)} connection records to {output_file}")
    
    except KeyboardInterrupt:
        print("\n\nCapture stopped by user")
    except Exception as e:
        print(f"\nError: {e}")
        print("\nTroubleshooting tips:")
        print("1. Run as Administrator (required for packet capture)")
        print("2. Install scapy: pip install scapy")
        print("3. Check if antivirus is blocking the script")
        print("4. Try running: python -c \"import scapy; print('OK')\"")

if __name__ == "__main__":
    main()
