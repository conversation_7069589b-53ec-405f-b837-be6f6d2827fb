#!/usr/bin/env powershell
# Xenimus Packet Capture PowerShell Script

Write-Host "Starting Xenimus packet capture for server *************" -ForegroundColor Green
Write-Host "Make sure to run this as Administrator for packet capture to work" -ForegroundColor Yellow
Write-Host ""

# Create captures directory if it doesn't exist
if (!(Test-Path "captures")) {
    New-Item -ItemType Directory -Path "captures"
    Write-Host "Created captures directory"
}

# Try to find Python
$pythonCommands = @("python", "python3", "py")
$pythonFound = $false

foreach ($cmd in $pythonCommands) {
    try {
        $null = Get-Command $cmd -ErrorAction Stop
        Write-Host "Found Python command: $cmd" -ForegroundColor Green
        
        # Generate timestamp for filename
        $timestamp = Get-Date -Format "yyyyMMdd_HHmm"
        $outputFile = "captures/xenimus_session_$timestamp.json"
        
        Write-Host "Starting capture... Press Ctrl+C to stop" -ForegroundColor Cyan
        Write-Host "Output file: $outputFile" -ForegroundColor Gray
        Write-Host ""
        
        # Run the packet capture
        & $cmd packet_capture.py -t ************* -o $outputFile
        
        $pythonFound = $true
        break
    }
    catch {
        continue
    }
}

if (-not $pythonFound) {
    Write-Host "ERROR: Python not found!" -ForegroundColor Red
    Write-Host ""
    Write-Host "Solutions:" -ForegroundColor Yellow
    Write-Host "1. Install Python from https://python.org (check 'Add to PATH')"
    Write-Host "2. Try running directly: py packet_capture.py -t ************* -o captures/xenimus_session.json"
    Write-Host "3. Use Wireshark with filter: ip.addr == *************"
    Write-Host ""
    Write-Host "Alternative: Use Wireshark for packet capture" -ForegroundColor Cyan
    Write-Host "Filter: ip.addr == *************"
}

Write-Host ""
Write-Host "Press any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
