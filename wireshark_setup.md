# Wireshark Setup for Xenimus Packet Analysis

## Installation
1. Download Wireshark from https://www.wireshark.org/
2. Install with WinPcap/Npcap for Windows packet capture

## Basic Setup for Game Traffic
1. Start Wireshark
2. Select your network interface (usually Ethernet or WiFi)
3. Start capture before launching Xenimus

## Filtering Xenimus Traffic
Use these display filters to isolate game traffic:

### By IP Address (if you know the server IP)
```
ip.addr == [server_ip]
```

### By Port Range (common game ports)
```
tcp.port >= 1024 and tcp.port <= 65535
```

### By Protocol
```
tcp or udp
```

### Combined Filter Example
```
(ip.addr == [server_ip]) and (tcp or udp)
```

## Analysis Tips
- Look for patterns in packet timing
- Check for unencrypted data in packet contents
- Monitor connection establishment/teardown
- Watch for repeated packet structures (game commands)

## Export Options
- File > Export Packet Dissections > As Plain Text
- File > Export Objects > HTTP (if applicable)
- Statistics > Protocol Hierarchy for traffic overview
